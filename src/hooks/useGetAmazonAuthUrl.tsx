import { useQuery } from "@tanstack/react-query";
import { getAccessToken } from "../lib/utils/index";
import { useAuth } from "./useAuth";
import { $http } from "../lib/https";

interface AmazonAuthUrlRequest {
  region?: string;
  testMode?: boolean;
}

export const useGetAmazonAuthUrl = ({
  AmazonAuthUrlRequest,
}: {
  AmazonAuthUrlRequest: AmazonAuthUrlRequest;
}) => {
  const { user } = useAuth();
  const queryKey = [`amazon-auth-url-${AmazonAuthUrlRequest?.region}`, user];
  const accessToken = getAccessToken();

  return useQuery({
    queryKey,
    queryFn: async () => {
      const response = await $http.get(
        `/amazon/api/v1/region?region=${AmazonAuthUrlRequest.region || "uk"}&test_mode=${AmazonAuthUrlRequest.testMode || false}`,
        {
          headers: {
            accept: "application/json",
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );

      if (response.data && typeof response.data === "string") {
        const authUrl = response.data.replace(/^"|"$/g, "");
        return { auth_url: authUrl };
      }

      throw new Error("Amazon authorization URL not found in response");
    },
    enabled: !!user,
  });
};
