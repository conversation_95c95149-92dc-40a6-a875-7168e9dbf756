import { useQuery } from "@tanstack/react-query";
import { getAccessToken } from "../lib/utils/index";
import { useAuth } from "./useAuth";
import { $http } from "../lib/https";

export const useGetMe = () => {
  const { user } = useAuth();
  const queryKey = [`get-me`, user];
  const accessToken = getAccessToken();

  return useQuery({
    queryKey,
    queryFn: async () => {
      const response = await $http.get(`/auth/api/v1/me`, {
        headers: {
          accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      });
      return response.data;
    },
    enabled: !!user,
  });
};
