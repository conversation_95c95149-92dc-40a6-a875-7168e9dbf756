import { $http } from "@/lib/https";
import { useQuery } from "@tanstack/react-query";
import { getAccessToken } from "../lib/utils/index";
import { useAuth } from "./useAuth";

interface ProfitCalcRequest {
  buy_box_price: number;
  fba_fees: number;
  referral_percent: number;
  seller_price: number;
  variable_closing_fee?: number;
  sales_rank: number;
  country?: string;
  vat?: number;
  asin_code: string;
  fullResponse: boolean;
}

export const useProfitCalculator = ({
  ProfitCalcRequest,
}: {
  ProfitCalcRequest: ProfitCalcRequest;
}) => {
  const { user } = useAuth();
  const queryKey = [
    `profit-calculator`,
    ProfitCalcRequest?.seller_price,
    ProfitCalcRequest.buy_box_price,
    ProfitCalcRequest.vat,
    ProfitCalcRequest.fba_fees,
    ProfitCalcRequest.referral_percent,
    user,
  ];

  const accessToken = getAccessToken();

  const queryParams = new URLSearchParams();
  queryParams.append(
    "buy_box_price",
    ProfitCalcRequest.buy_box_price.toString(),
  );
  queryParams.append("fba_fees", ProfitCalcRequest.fba_fees.toString());
  queryParams.append(
    "referral_percent",
    ProfitCalcRequest.referral_percent.toString(),
  );
  queryParams.append("seller_price", ProfitCalcRequest.seller_price.toString());
  if (ProfitCalcRequest.variable_closing_fee !== undefined) {
    queryParams.append(
      "variable_closing_fee",
      ProfitCalcRequest.variable_closing_fee.toString(),
    );
  }
  if (
    typeof ProfitCalcRequest.sales_rank === "number" &&
    !isNaN(ProfitCalcRequest.sales_rank)
  ) {
    queryParams.append("sales_rank", ProfitCalcRequest.sales_rank.toString());
  }
  queryParams.append("country", ProfitCalcRequest.country || "UK");
  const vatValue =
    ProfitCalcRequest.vat !== undefined ? ProfitCalcRequest.vat : 20;
  queryParams.append("vat", vatValue.toString());

  return useQuery({
    queryKey,
    queryFn: async () => {
      const response = await $http.get(
        `/extension-backend/api/v1/kepa/profit-calculator?${queryParams.toString()}`,
        {
          headers: {
            accept: "application/json",
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
        },
      );
      return response.data;
    },
    enabled: !!user,
  });
};
