"use client";
import AccordionData from "@/components/extension/accordion-data";
import Header from "@/components/extension/Header";
import Nav from "@/components/extension/Nav";
import Overview from "@/components/extension/product-overview";
import OverViewHeader from "@/components/extension/overview-header";
import SystemiseFulfilmentAd from "@/components/extension/systemise-fulfillments-ads";
import { useProductContext } from "@/hooks/useProduct";
import { useGetMe } from "@/hooks/useGetMe";
import AmazonVerificationModal from "@/components/extension/amazon-verification-modal";
import { useState } from "react";
import { AlertCircle } from "lucide-react";

const Home = () => {
  const { dataLoadingProgress } = useProductContext();
  const { data, isLoading } = useGetMe();
  const [showAmazonModal, setShowAmazonModal] = useState(false);

  const content = (
    <main className="flex flex-col h-screen max-h-screen overflow-scroll bg-gray-100">
      <AmazonVerificationModal
        isOpen={showAmazonModal}
        onClose={() => setShowAmazonModal(false)}
        isDarkMode={false}
      />
      {dataLoadingProgress === 100 && (
        <div className="flex-shrink-0">
          <Nav />
          <Header />
          <OverViewHeader />
          <Overview />
          <AccordionData />
          <SystemiseFulfilmentAd />
        </div>
      )}
      {dataLoadingProgress !== 100 && (
        <div className="p-6 h-screen flex items-center justify-center relative">
          <h1 className="font-bold text-black/80 -mt-12 text-[1.6rem] tracking-wide animate-pulse">
            CLICKBUY
          </h1>
          <div className="absolute top-50 left-50 w-[90%] h-2 bg-gray-200 rounded-full overflow-hidden">
            <div
              className="h-full bg-green-500 transition-all duration-300"
              style={{ width: `${dataLoadingProgress}%` }}
            />
          </div>
        </div>
      )}
    </main>
  );

  return (
    <div className="relative">
      {content}
      {data && !data.is_verified && (
        <div className="absolute inset-0 z-10 flex justify-center items-center bg-black/80">
          <div className="max-w-md w-full mx-4 p-8 rounded-lg border-2 bg-white border-gray-200 shadow-2xl text-center h-fit">
            <div className="flex justify-center mb-8">
              <div className="p-4 rounded-full bg-black/20 text-black">
                <AlertCircle size={20} />
              </div>
            </div>
            <h3 className="text-md font-bold text-gray-800 mb-4">
              Amazon Account Required
            </h3>
            <p className="text-gray-600 mb-8 text-sm">
              To access AI Deals Sourcing features, you need to connect your
              Amazon Seller Central account first.
            </p>
            <button
              onClick={() => setShowAmazonModal(true)}
              className="w-full px-8 py-4 rounded-lg font-semibold text-sm transition-all duration-300 text-white hover:from-amber-600 hover:to-amber-700 hover:shadow-lg hover:-translate-y-1 active:translate-y-0 bg-black"
            >
              Connect Amazon Account
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default Home;
