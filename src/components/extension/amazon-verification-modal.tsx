/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useState } from "react";
import { X, ExternalLink, AlertCircle, CheckCircle } from "lucide-react";
import { useGetAmazonAuthUrl } from "@/hooks/useGetAmazonAuthUrl";

interface AmazonVerificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  isDarkMode?: boolean;
}

const AmazonVerificationModal: React.FC<AmazonVerificationModalProps> = ({
  isOpen,
  onClose,
  isDarkMode = true,
}) => {
  const [selectedRegion, setSelectedRegion] = useState<string>("uk");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>("");

  const regions = [
    { value: "uk", label: "United Kingdom", flag: "🇬🇧" },
    // { value: "us", label: "United States", flag: "🇺🇸" },
  ];

  const handleConnect = async () => {
    if (!selectedRegion) {
      setError("Please select a region");
      return;
    }

    setIsLoading(true);
    setError("");

    try {
    	const response = await useGetAmazonAuthUrl({selectedRegion, false});

    	if (response.auth_url) {
    		window.open(response.auth_url, "_blank");
    	} else {
    		throw new Error("No authorization URL received");
    	}
    } catch (error: any) {
    	setError(error.message || "Failed to connect to Amazon");
    	setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div
        className={`fixed inset-0 bg-black/40 transition-opacity`}
        onClick={onClose}
      />

      <div className="flex min-h-full items-center justify-center p-4">
        <div
          className={`relative w-full max-w-md transform rounded-md border shadow-2 xl bg-white border-gray-200
					}`}
        >
          <div className="flex items-center justify-between p-6 pb-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full flex items-center justify-center bg-black/10">
                <ExternalLink size={20} className="text-black" />
              </div>
              <div>
                <h3
                  className={`text-lg font-semibold ${
                    isDarkMode ? "text-white" : "text-gray-900"
                  }`}
                >
                  Connect Amazon Account
                </h3>
                <p
                  className={`text-sm ${
                    isDarkMode ? "text-gray-300" : "text-gray-600"
                  }`}
                >
                  Required for Extensions Dashboard
                </p>
              </div>
            </div>

            <button
              onClick={onClose}
              className={`p-2 rounded-lg transition-colors ${
                isDarkMode
                  ? "hover:bg-gray-700 text-gray-400 hover:text-white"
                  : "hover:bg-gray-100 text-gray-500 hover:text-gray-700"
              }`}
            >
              <X size={18} />
            </button>
          </div>

          <div className="px-6 pb-6">
            <div
              className={`mb-6 p-4 rounded-lg border bg-black/10 border-black/20`}
            >
              <div className="flex items-start gap-3">
                <AlertCircle size={16} className="mt-0.5 text-[#19D86C]" />
                <div className="text-sm">
                  <p className={`font-medium mb-1 text-gray-900`}>
                    Amazon Account Required
                  </p>
                  <p className={` "text-gray-600`}>
                    To access the Extensions Dashboard and use our AI-powered
                    product analysis tools, you need to connect your Amazon
                    Seller Central account.
                  </p>
                </div>
              </div>
            </div>

            <div className="mb-6">
              <label
                className={`block text-sm font-medium mb-3 text-gray-800 `}
              >
                Select your Amazon marketplace region:
              </label>

              <div className="space-y-2">
                {regions.map((region) => (
                  <button
                    key={region.value}
                    type="button"
                    onClick={() => setSelectedRegion(region.value)}
                    className={`w-full p-4 rounded-lg border transition-all text-left flex items-center gap-3 ${
                      selectedRegion === region.value
                        ? "border-black/20 bg-black/10"
                        : "border-gray-200 hover:border-gray-300"
                    }`}
                  >
                    <span className="text-2xl">{region.flag}</span>
                    <div className="flex-1">
                      <div
                        className={`font-medium ${
                          isDarkMode ? "text-white" : "text-gray-900"
                        }`}
                      >
                        {region.label}
                      </div>
                      <div
                        className={`text-sm ${
                          isDarkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Amazon {region.value.toUpperCase()} marketplace
                      </div>
                    </div>
                    {selectedRegion === region.value && (
                      <CheckCircle size={20} className="text-black" />
                    )}
                  </button>
                ))}
              </div>
            </div>

            {error && (
              <div
                className={`mb-4 p-3 rounded-lg border ${
                  isDarkMode
                    ? "bg-red-500/10 border-red-500/30 text-red-400"
                    : "bg-red-50 border-red-200 text-red-600"
                }`}
              >
                <div className="flex items-center gap-2">
                  <AlertCircle size={16} />
                  <span className="text-sm font-medium">{error}</span>
                </div>
              </div>
            )}

            <div className="flex flex-col gap-2">
              <button
                onClick={handleConnect}
                disabled={isLoading || !selectedRegion}
                className="flex-1 px-2 py-3 cursor-pointer rounded-md font-medium transition-all duration-200 bg-black text-white hover:from-[#12C2E9] hover:to-[#19D86C] hover:scale-105 active:scale-95 disabled:opacity-50 disabled:hover:scale-100 flex items-center justify-center gap-2"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[#111215]"></div>
                    <span className="text-sm">Connecting...</span>
                  </>
                ) : (
                  <>
                    <ExternalLink size={14} />
                    <span className="text-sm">Connect to Amazon</span>
                  </>
                )}
              </button>
              <button
                onClick={onClose}
                disabled={isLoading}
                className={`flex-1 px-2 py-3 cursor-pointer rounded-lg font-medium transition-all duration-200 bg-gray-100 text-gray-700 hover:bg-gray-200 disabled:opacity-50 text-sm`}
              >
                Cancel
              </button>
            </div>

            <div
              className={`mt-4 text-xs text-center ${
                isDarkMode ? "text-gray-400" : "text-gray-500"
              }`}
            >
              You&apos;ll be redirected to Amazon Seller Central to authorize
              the connection.
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AmazonVerificationModal;
