"use client";

import React from "react";
import { X } from "lucide-react";

interface ComingSoonProps {
  isOpen: boolean;
  onClose: () => void;
}

const ComingSoon: React.FC<ComingSoonProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 p-6 text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Coming Soon!</h2>
        <p className="text-gray-600 mb-6">
          We're working hard to bring you this feature. Stay tuned for updates!
        </p>

        <div className="flex justify-center">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-black text-white rounded-lg hover:bg-black/80"
          >
            Got it
          </button>
        </div>
      </div>
    </div>
  );
};

export default ComingSoon;
